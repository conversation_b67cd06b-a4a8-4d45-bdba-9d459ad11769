package common

import (
	"cpn-ai/common/utils"
	"errors"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/shopspring/decimal"

	"github.com/google/uuid"
)

var ErrRecordNotFound = errors.New("record not found")

var StartTime = time.Now().Unix() // unit: second
var DefaultTime = time.Date(1900, time.January, 1, 0, 0, 0, 0, time.Local)
var NationalDay = time.Date(1949, time.October, 1, 0, 0, 0, 0, time.Local)
var InvoiceRechargeStartTime = time.Date(2025, time.May, 9, 0, 0, 0, 0, time.UTC)

var Version = "v1.1.9" // this hard coding will be replaced automatically when building, no need to manually change
var SystemName = "CPN AI"
var ServerAddress = "http://localhost:3008"
var Footer = ""
var Logo = ""
var TopUpLink = ""
var ChatLink = ""
var QuotaPerUnit = 500 * 1000.0 // $0.002 / 1K tokens
var DisplayInCurrencyEnabled = true
var DisplayTokenStatEnabled = true

// var HubServerAddress = "*************"
var HubServerAddress = "*************"
var SiteDomain = "chenyu.cn"

var Cipher = "3123"

var CacheShutdownKeepSeconds = int64(1 * 60 * 60 * 24 * 2)
var CacheShutdownKeepSeconds7Day = int64(1 * 60 * 60 * 24 * 7)

const (
	SizeB2KB = 1024
	SizeB2M  = 1024 * 1024
	SizeB2G  = 1024 * 1024 * 1024
	SizeB2T  = 1024 * 1024 * 1024 * 1024
)

//var HubServerAddress = "************"

//## **镜像储存**
//有提供30G免费的镜像空间，超出部分的收费更是非常便宜的:
//仙宫云0.00036元/GB/小时
//腾讯云0.0025元/GB/小时

// var ImageStoreLimitSize = decimal.NewFromInt(300 * 1024 * 1024 * 1024) //300G封顶的镜像空间
var ImageStoreLimitSize = decimal.NewFromInt(1000 * 1024 * 1024 * 1024) //300G封顶的镜像空间
var ImageStoreFreeSize = decimal.NewFromInt(50 * 1024 * 1024 * 1024)    //50G免费的镜像空间
var ImageStoreHourPrice = decimal.NewFromFloat(0.00036)                 //算云镜像0.00036元/GB/小时

var CloudStoreLimitSize = decimal.NewFromInt(300 * 1024 * 1024 * 1024) //300G封顶的算云空间
var CloudStoreFreeSize = decimal.NewFromInt(50 * 1024 * 1024 * 1024)   //50G免费的算云空间
var CloudStoreHourPrice = decimal.NewFromFloat(0.00036)                //算云存储0.00036元/GB/小时

var NoCardPrice = decimal.NewFromFloat(0.3)  //无卡启动算力扣费金额
var WarnAmount = decimal.NewFromInt(10)      //低于10元时提醒用户
var LimitDebtAmount = decimal.NewFromInt(-5) //最高欠款金额

const (
	InstanceOverDays = 7
)

// Any options with "Secret", "Token" in its key won't be return by GetOptions

var SessionSecret = uuid.New().String()

var CacheMap map[string]interface{}

var OptionMap map[string]string
var OptionMapRWMutex sync.RWMutex

var ItemsPerPage = 10
var MaxRecentItems = 100

var PasswordLoginEnabled = true
var PasswordRegisterEnabled = true
var EmailVerificationEnabled = false
var GitHubOAuthEnabled = false
var WeChatAuthEnabled = false
var TurnstileCheckEnabled = false
var RegisterEnabled = true

var EmailDomainRestrictionEnabled = false
var EmailDomainWhitelist = []string{
	"gmail.com",
	"163.com",
	"126.com",
	"qq.com",
	"outlook.com",
	"hotmail.com",
	"icloud.com",
	"yahoo.com",
	"foxmail.com",
}

var DebugEnabled = os.Getenv("DEBUG") == "true"
var MemoryCacheEnabled = os.Getenv("MEMORY_CACHE_ENABLED") == "true"

var LogConsumeEnabled = true

var SMTPServer = ""
var SMTPPort = 587
var SMTPAccount = ""
var SMTPFrom = ""
var SMTPToken = ""

var GitHubClientId = ""
var GitHubClientSecret = ""

var WeChatServerAddress = ""
var WeChatServerToken = ""
var WeChatAccountQRCodeImageURL = ""

var TurnstileSiteKey = ""
var TurnstileSecretKey = ""

var QuotaForNewUser = 0
var QuotaForInviter = 0
var QuotaForInvitee = 0
var ChannelDisableThreshold = 5.0
var AutomaticDisableChannelEnabled = false
var QuotaRemindThreshold = 1000
var PreConsumedQuota = 500
var ApproximateTokenEnabled = false
var RetryTimes = 0

var RootUserEmail = ""

var IsMasterNode = os.Getenv("NODE_TYPE") != "slave"

var requestInterval, _ = strconv.Atoi(os.Getenv("POLLING_INTERVAL"))
var RequestInterval = time.Duration(requestInterval) * time.Second

var SyncFrequency = utils.GetOrDefault("SYNC_FREQUENCY", 10*60) // unit is second

var BatchUpdateEnabled = false
var BatchUpdateInterval = utils.GetOrDefault("BATCH_UPDATE_INTERVAL", 5)

var RelayTimeout = utils.GetOrDefault("RELAY_TIMEOUT", 0) // unit is second

const (
	RequestIdKey = "X-Cpnai-Request-Id"
)

const (
	RoleGuestUser  = 0
	RoleCommonUser = 1
	RoleAdminUser  = 10
	RoleRootUser   = 100
)

var (
	FileUploadPermission    = RoleGuestUser
	FileDownloadPermission  = RoleGuestUser
	ImageUploadPermission   = RoleGuestUser
	ImageDownloadPermission = RoleGuestUser
)

// All duration's unit is seconds
// Shouldn't larger then RateLimitKeyExpirationDuration
var (
	GlobalApiRateLimitNum            = utils.GetOrDefault("GLOBAL_API_RATE_LIMIT", 180*1000)
	GlobalApiRateLimitDuration int64 = 3 * 60

	GlobalWebRateLimitNum            = utils.GetOrDefault("GLOBAL_WEB_RATE_LIMIT", 60*1000)
	GlobalWebRateLimitDuration int64 = 3 * 60

	UploadRateLimitNum            = 10
	UploadRateLimitDuration int64 = 60

	DownloadRateLimitNum            = 10
	DownloadRateLimitDuration int64 = 60

	CriticalRateLimitNum            = 20
	CriticalRateLimitDuration int64 = 20 * 60
)

var RateLimitKeyExpirationDuration = 20 * time.Minute

const (
	UserStatusEnabled  = 1 // don't use 0, 0 is the default value!
	UserStatusDisabled = 2 // also don't use 0
	UserStatusDestroy  = 4 //用户注销
)

const (
	TokenStatusEnabled   = 1 // don't use 0, 0 is the default value!
	TokenStatusDisabled  = 2 // also don't use 0
	TokenStatusExpired   = 3
	TokenStatusExhausted = 4
)

const (
	RedemptionCodeStatusEnabled  = 1 // don't use 0, 0 is the default value!
	RedemptionCodeStatusDisabled = 2 // also don't use 0
	RedemptionCodeStatusUsed     = 3 // also don't use 0
)

const (
	ChannelStatusUnknown          = 0
	ChannelStatusEnabled          = 1 // don't use 0, 0 is the default value!
	ChannelStatusManuallyDisabled = 2 // also don't use 0
	ChannelStatusAutoDisabled     = 3
)

const (
	ChannelTypeUnknown        = 0
	ChannelTypeOpenAI         = 1
	ChannelTypeAPI2D          = 2
	ChannelTypeAzure          = 3
	ChannelTypeCloseAI        = 4
	ChannelTypeOpenAISB       = 5
	ChannelTypeOpenAIMax      = 6
	ChannelTypeOhMyGPT        = 7
	ChannelTypeCustom         = 8
	ChannelTypeAILS           = 9
	ChannelTypeAIProxy        = 10
	ChannelTypePaLM           = 11
	ChannelTypeAPI2GPT        = 12
	ChannelTypeAIGC2D         = 13
	ChannelTypeAnthropic      = 14
	ChannelTypeBaidu          = 15
	ChannelTypeZhipu          = 16
	ChannelTypeAli            = 17
	ChannelTypeXunfei         = 18
	ChannelType360            = 19
	ChannelTypeOpenRouter     = 20
	ChannelTypeAIProxyLibrary = 21
	ChannelTypeFastGPT        = 22
	ChannelTypeTencent        = 23
)

const (
	InvoiceSuccessEmail = `
			<!doctype html>
			<html>
				<head>
				  <meta charset="UTF-8">
				  <title>发票开具成功通知</title>
				</head>
				<body style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6;">
				  <div>
					主题：发票开具成功通知<br><br>
					尊敬的客户，<br><br>
					您好！<br><br>
					很高兴通知您，您的发票已成功开具。<br><br>
					请点击下面的链接下载您的发票：<br>
					<a href="{{.InvoiceURL}}">下载发票</a><br><br>
					您的发票申请订单号如下：<br>
						{{range .Orders}}{{.}}<br>{{end}}<br><br><br>
					如果您有任何疑问或需要进一步的帮助，请随时与我们联系。<br><br>
					祝您生活愉快！<br><br>
					[晨羽智云]
				  </div>
				</body>
			</html>
		`
	InvoiceFailedEmail = `
			<!doctype html>
			<html>
				<head>
				  <meta charset="UTF-8">
				  <title>发票开具失败通知</title>
				</head>
				<body style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6;">
				  <div>
					主题：发票开具失败通知<br><br>
					尊敬的客户，<br><br>
					您好！<br><br>
					很遗憾通知您，您的发票申请订单号如下：<br>
					{{range .Orders}}{{.}}<br>{{end}}<br>
					<p style="color: red">未能成功开具发票</p>。<br><br>
					失败原因如下：<br>
					{{.Remark}}<br><br>
					请根据以上说明检查并改正问题，或直接联系我们的客服获取进一步支持。<br><br>
					感谢您的理解与配合！<br><br>
					祝您生活愉快！<br><br>
					[晨羽智云]
				  </div>
				</body>
			</html>
		`
	WarnMail = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pod 告警通知 - {{.PodName}}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .header {
            background-color: #1e88e5;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .info-card {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #1e88e5;
            background-color: #f5f7fa;
            border-radius: 4px;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-group {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px 20px;
            margin-bottom: 15px;
        }
        .label {
            font-weight: bold;
            color: #555;
        }
        .value {
            color: #333;
            word-break: break-all;
        }
        .log-container {
            background-color: #212121;
            color: #f1f1f1;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        .footer {
            margin-top: 30px;
            padding: 15px;
            background-color: #f5f7fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        .footer a {
            color: #1e88e5;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .red {
            color: #d32f2f;
        }
        .blue {
            color: #1e88e5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="red">[告警通知]</span> Pod 异常</h1>
        </div>
        
        <div class="info-card">
            <div class="info-section">
                <h2>Pod 基本信息</h2>
                <div class="info-group">
                    <div class="label">Pod 名称:</div>
                    <div class="value">{{.PodName}}</div>
                </div>
                <div class="info-group">
                    <div class="label">报警时间:</div>
                    <div class="value">{{.AlertTime}}</div>
                </div>
                <div class="info-group">
                    <div class="label">启动标记:</div>
                    <div class="value">{{.StartTag}}</div>
                </div>
                <div class="info-group">
                    <div class="label">实例 UUID:</div>
                    <div class="value">{{.InstanceUUID}}</div>
                </div>
            </div>
            
            <div class="info-section">
                <h2>详细信息</h2>
                <div class="info-group">
                    <div class="label">Docker ID:</div>
                    <div class="value">{{.DockerID}}</div>
                </div>
                <div class="info-group">
                    <div class="label">Node ID:</div>
                    <div class="value">{{.NodeID}}</div>
                </div>
                <div class="info-group">
                    <div class="label">Virtual ID:</div>
                    <div class="value">{{.VirtualID}}</div>
                </div>
                <div class="info-group">
                    <div class="label">Pod ID:</div>
                    <div class="value">{{.PodID}}</div>
                </div>
                <div class="info-group">
                    <div class="label">Image ID:</div>
                    <div class="value">{{.ImageID}}</div>
                </div>
            </div>
        </div>
        
        <div class="log-container">
            <h3>告警日志</h3>
            <pre>{{.LogContent}}</pre>
        </div>
        
        <div class="footer">
            <p><strong>晨羽智云（杭州）科技有限公司</strong></p>
            <p>地址：浙江省杭州市西湖区教工路88号 10楼1006室</p>
            <p>联系电话：132-0571-2110 | 邮箱：<EMAIL></p>
        </div>
    </div>
</body>
</html>
		`
)

const AlarmEmailTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算云警报通知</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
            margin: -20px -20px 20px -20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .summary {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
            color: #856404;
        }
        .summary-value {
            color: #856404;
            margin-left: 5px;
        }
        .alarm-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .alarm-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: bold;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }
        .alarm-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }
        .alarm-table tr:hover {
            background-color: #f8f9fa;
        }
        .alarm-key {
            font-weight: bold;
            color: #dc3545;
            word-break: break-all;
        }
        .alarm-content {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: "Courier New", monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            word-break: break-all;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        .footer p {
            margin: 5px 0;
        }
        .warning-icon {
            color: #ffc107;
            font-size: 18px;
            margin-right: 5px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .summary-item {
                display: block;
                margin-right: 0;
            }
            .alarm-table {
                font-size: 14px;
            }
            .alarm-table th,
            .alarm-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="warning-icon">⚠️</span>算云警报通知</h1>
        </div>

        <div class="summary">
            <div class="summary-item">
                <span class="summary-label">警报数量:</span>
                <span class="summary-value">{{.Count}} 条</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">发送时间:</span>
                <span class="summary-value">{{.SendTime}}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">服务器IP:</span>
                <span class="summary-value">{{.LocalIP}}</span>
            </div>
        </div>

        {{if .AlarmList}}
        <table class="alarm-table">
            <thead>
                <tr>
                    <th style="width: 40%;">警报标识</th>
                    <th style="width: 60%;">详细内容</th>
                </tr>
            </thead>
            <tbody>
                {{range $index, $alarm := .AlarmList}}
                <tr>
                    <td class="alarm-key">{{$alarm.Key}}</td>
                    <td><div class="alarm-content">{{$alarm.Content}}</div></td>
                </tr>
                {{end}}
            </tbody>
        </table>
        {{end}}

        <div class="footer">
            <p><strong>晨羽智云（杭州）科技有限公司</strong></p>
            <p>地址：浙江省杭州市西湖区教工路88号 10楼1006室</p>
            <p>联系电话：132-0571-2110 | 邮箱：<EMAIL></p>
            <p style="margin-top: 15px; color: #999;">
                此邮件为系统自动发送，请勿直接回复。如有疑问，请联系技术支持。
            </p>
        </div>
    </div>
</body>
</html>
`

const InstanceShutdownFailEmailTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实例关闭失败通知</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
            margin: -20px -20px 20px -20px;
        }
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        .alert-icon {
            color: #ffc107;
            font-size: 24px;
            margin-right: 8px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th,
        .info-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            width: 30%;
        }
        .info-table td {
            background-color: #fff;
        }
        .error-message {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 4px;
            margin: 15px 0;
            word-break: break-all;
        }
        .footer {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        .footer p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="alert-icon">🚨</span>实例关闭失败通知</h1>
        </div>

        <table class="info-table">
            <tr>
                <th>记录ID</th>
                <td>{{.RecordID}}</td>
            </tr>
            <tr>
                <th>实例ID</th>
                <td>{{.InstanceID}}</td>
            </tr>
            <tr>
                <th>启动标识</th>
                <td>{{.StartupMark}}</td>
            </tr>
            <tr>
                <th>检测时间</th>
                <td>{{.CheckTime}}</td>
            </tr>
            <tr>
                <th>关闭时间</th>
                <td>{{.ShutdownTime}}</td>
            </tr>
        </table>

        <div class="error-message">
            <strong>错误信息：</strong><br>
            {{.ErrorMessage}}
        </div>

        <div class="footer">
            <p><strong>晨羽智云（杭州）科技有限公司</strong></p>
            <p>此邮件为系统自动发送，请及时处理相关问题。</p>
        </div>
    </div>
</body>
</html>
`

const (
	EmailContentTypeHtml = "text/html"
	EmailContentTypeText = "text/plain"
)
